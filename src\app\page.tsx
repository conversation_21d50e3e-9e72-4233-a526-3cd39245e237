import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Code, Palette, ExternalLink, Zap } from "lucide-react";
import Particles from "@/components/particles";

export default function Home() {
  // Sample data - replace with your actual information
  const featuredProjects = [
    {
      title: "NEON COMMERCE",
      description: "Full-stack e-commerce platform with cyberpunk aesthetics and real-time inventory",
      technologies: ["Next.js", "TypeScript", "MongoDB", "Stripe", "Socket.io"],
      image: "/api/placeholder/400/250",
      link: "/projects",
    },
    {
      title: "CYBER TASK MANAGER",
      description: "Collaborative task management with holographic UI and neural sync",
      technologies: ["React", "Node.js", "PostgreSQL", "WebRTC", "AI"],
      image: "/api/placeholder/400/250",
      link: "/projects",
    },
    {
      title: "NEXUS BANKING UI",
      description: "Futuristic banking interface with biometric auth and neural security",
      technologies: ["React Native", "Figma", "Blockchain", "3D Graphics"],
      image: "/api/placeholder/400/250",
      link: "/projects",
    },
  ];

  const skills = [
    { name: "NEURAL DEVELOPMENT", level: 95, icon: Code },
    { name: "CYBER UX/UI", level: 90, icon: Palette },
    { name: "QUANTUM BACKEND", level: 85, icon: Zap },
    { name: "HOLO INTERFACE", level: 80, icon: Zap },
  ];

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Particle Background */}
      <Particles />
      
      {/* Animated Grid Background */}
      <div className="fixed inset-0 pointer-events-none z-1">
        <div className="absolute inset-0 opacity-20">
          <div className="h-full w-full" style={{
            backgroundImage: `
              linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            animation: 'grid-move 20s linear infinite'
          }} />
        </div>
      </div>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Hero Glow Effect */}
        <div className="hero-glow"></div>
        
        {/* Scan Lines */}
        <div className="scan-line"></div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="animate-fadeInUp">
              <div className="mb-6">
                <span className="tech-tag inline-block mb-4">SYSTEM ONLINE</span>
              </div>
              <h1 className="text-4xl lg:text-6xl font-bold cyber-heading mb-6">
                <span className="gradient-text">CYBER</span>
                <br />
                <span className="text-primary">DEVELOPER</span>
              </h1>
              <p className="text-xl lg:text-2xl cyber-body text-secondary mb-4">
                [Your Professional Tagline]
              </p>
              <p className="cyber-body text-secondary mb-10 max-w-lg">
                Crafting digital experiences in the neon-lit corridors of cyberspace. 
                Full-stack architect and UX designer building the future, one pixel at a time.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button asChild size="lg" className="cyber-button text-lg">
                  <Link href="/projects">
                    <Zap className="mr-2 h-5 w-5" />
                    ACCESS PORTFOLIO
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="cyber-button text-lg">
                  <Link href="/contact">
                    <ArrowRight className="mr-2 h-5 w-5" />
                    INITIATE CONTACT
                  </Link>
                </Button>
              </div>
            </div>
            <div className="animate-scaleIn">
              <div className="glass-card p-8 relative overflow-hidden">
                <div className="scan-line"></div>
                <div className="aspect-square bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl flex items-center justify-center relative">
                  <div className="text-center z-10">
                    <div className="w-32 h-32 mx-auto mb-4 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center animate-pulse-glow">
                      <span className="text-4xl font-bold cyber-heading text-white">YN</span>
                    </div>
                    <p className="cyber-mono text-primary">NEURAL AVATAR</p>
                    <div className="mt-4">
                      <span className="tech-tag">STATUS: ACTIVE</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Projects */}
      <section className="py-20 relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <span className="tech-tag inline-block mb-4">FEATURED MODULES</span>
            <h2 className="text-3xl lg:text-4xl font-bold cyber-heading gradient-text mb-4">
              CYBER PROJECTS
            </h2>
            <p className="cyber-body text-secondary max-w-2xl mx-auto">
              Holographic projections of my latest digital constructs and neural interfaces.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredProjects.map((project, index) => (
              <Card key={index} className={`glass-card hover-lift animate-fadeInUp stagger-${index + 1}`}>
                <div className="scan-line"></div>
                <CardHeader className="p-0">
                  <div className="aspect-video bg-gradient-to-br from-primary/20 to-secondary/20 rounded-t-xl flex items-center justify-center">
                    <span className="cyber-mono text-primary">HOLOGRAPHIC DISPLAY</span>
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <CardTitle className="mb-2 cyber-subheading text-primary hover-glow">
                    {project.title}
                  </CardTitle>
                  <CardDescription className="cyber-body text-secondary mb-4">
                    {project.description}
                  </CardDescription>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.technologies.map((tech, techIndex) => (
                      <Badge key={techIndex} className="tech-tag">
                        {tech}
                      </Badge>
                    ))}
                  </div>
                  <Button asChild variant="outline" size="sm" className="cyber-button w-full">
                    <Link href={project.link}>
                      <ExternalLink className="mr-2 h-4 w-4" />
                      ACCESS SYSTEM
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Button asChild variant="outline" size="lg" className="cyber-button">
              <Link href="/projects">
                <ArrowRight className="mr-2 h-5 w-5" />
                VIEW ALL SYSTEMS
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Skills Overview */}
      <section className="py-20 relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <span className="tech-tag inline-block mb-4">NEURAL CAPABILITIES</span>
            <h2 className="text-3xl lg:text-4xl font-bold cyber-heading gradient-text mb-4">
              CYBER SKILLS
            </h2>
            <p className="cyber-body text-secondary max-w-2xl mx-auto">
              My technical arsenal and digital proficiencies across the full development spectrum.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {skills.map((skill, index) => (
              <Card key={index} className={`glass-card text-center hover-lift animate-fadeInUp stagger-${index + 1}`}>
                <div className="scan-line"></div>
                <CardContent className="p-6">
                  <skill.icon className="h-12 w-12 mx-auto mb-4 text-primary animate-glow" />
                  <h3 className="cyber-subheading text-primary mb-2">{skill.name}</h3>
                  <div className="w-full bg-muted rounded-full h-2 mb-2">
                    <div 
                      className="bg-gradient-to-r from-primary to-secondary h-2 rounded-full transition-all duration-1000 ease-out progress-animate"
                      style={{ width: `${skill.level}%` }}
                    ></div>
                  </div>
                  <p className="cyber-mono text-primary">{skill.level}% SYNC</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 relative z-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <Card className="glass-card">
            <div className="scan-line"></div>
            <CardContent className="p-8">
              <span className="tech-tag inline-block mb-4">INITIALIZE PROTOCOL</span>
              <h2 className="text-3xl lg:text-4xl font-bold cyber-heading gradient-text mb-4">
                READY TO CONNECT?
              </h2>
              <p className="cyber-body text-secondary mb-8 max-w-2xl mx-auto">
                Let's synchronize our neural networks and create something extraordinary in the digital realm.
              </p>
              <Button asChild size="lg" className="cyber-button text-lg">
                <Link href="/contact">
                  <Zap className="mr-2 h-5 w-5" />
                  ESTABLISH CONNECTION
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}