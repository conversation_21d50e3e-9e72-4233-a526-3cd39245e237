import Link from "next/link";
import { Gith<PERSON>, Linkedin, Twitter, Mail } from "lucide-react";

const navigation = {
  main: [
    { name: "Home", href: "/" },
    { name: "About", href: "/about" },
    { name: "Projects", href: "/projects" },
    { name: "Skills", href: "/skills" },
    { name: "Services", href: "/services" },
    { name: "Contact", href: "/contact" },
  ],
  social: [
    {
      name: "GitHub",
      href: "https://github.com/yourusername",
      icon: Github,
    },
    {
      name: "LinkedIn",
      href: "https://linkedin.com/in/yourusername",
      icon: Linkedin,
    },
    {
      name: "Twitter",
      href: "https://twitter.com/yourusername",
      icon: Twitter,
    },
    {
      name: "Email",
      href: "mailto:<EMAIL>",
      icon: Mail,
    },
  ],
};

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="glass-card border-t border-t-cyan-500/20 mt-20">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="md:col-span-2">
            <div className="flex items-center mb-4">
              <span className="text-2xl font-bold cyber-heading gradient-text">
                CYBER
              </span>
              <span className="text-2xl font-bold cyber-heading text-primary ml-1">
                PORT
              </span>
            </div>
            <p className="cyber-body text-secondary mb-4 max-w-md">
              A passionate full-stack developer and UX/UI designer creating beautiful, functional digital experiences in the digital realm.
            </p>
            <div className="flex space-x-4">
              {navigation.social.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="text-secondary hover:text-primary transition-colors duration-200 hover-glow"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <item.icon className="h-6 w-6" aria-hidden="true" />
                </a>
              ))}
            </div>
          </div>

          {/* Navigation Links */}
          <div>
            <h3 className="cyber-subheading text-primary mb-4">NAVIGATION</h3>
            <ul className="space-y-2">
              {navigation.main.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="cyber-body text-secondary hover:text-primary transition-colors duration-200 hover-glow"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="cyber-subheading text-primary mb-4">CONNECT</h3>
            <ul className="space-y-2">
              <li>
                <a
                  href="mailto:<EMAIL>"
                  className="cyber-body text-secondary hover:text-primary transition-colors duration-200 hover-glow"
                >
                  <EMAIL>
                </a>
              </li>
              <li>
                <span className="cyber-body text-secondary">
                  Available for freelance work
                </span>
              </li>
              <li>
                <span className="tech-tag inline-block mt-2">STATUS: ONLINE</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-8 pt-8 border-t border-t-cyan-500/20">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <p className="cyber-mono text-tertiary text-sm">
              © {currentYear} CYBERPORT. ALL RIGHTS RESERVED.
            </p>
            <div className="flex space-x-4 mt-4 sm:mt-0">
              <span className="tech-tag">v2.0.1</span>
              <span className="tech-tag">NEXT.JS</span>
              <span className="tech-tag">CYBERPUNK</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}